package handlers

import (
	"net/http"
	"strings"

	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db *gorm.DB
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB) *AuthHandler {
	return &AuthHandler{db: db}
}

// RegisterResponse represents the response for user registration
type RegisterResponse struct {
	User  models.UserResponse `json:"user"`
	Token string              `json:"token"`
}

// LoginResponse represents the response for user login
type LoginResponse struct {
	User  models.UserResponse `json:"user"`
	Token string              `json:"token"`
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserRegistration
	
	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Validate username
	req.Username = strings.TrimSpace(req.Username)
	if len(req.Username) < 3 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Username must be at least 3 characters long",
		})
		return
	}

	// Check if username already exists
	existingUser, err := models.GetUserByUsername(h.db, req.Username)
	if err == nil && existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Username already exists",
		})
		return
	}

	// Create user
	user, err := models.CreateUser(h.db, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create user",
			"details": err.Error(),
		})
		return
	}

	// Generate JWT token
	token, err := middleware.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate token",
		})
		return
	}

	// Return response
	response := RegisterResponse{
		User:  user.ToResponse(),
		Token: token,
	}

	c.JSON(http.StatusCreated, response)
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.UserLogin
	
	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Authenticate user
	user, err := models.AuthenticateUser(h.db, req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid username or password",
		})
		return
	}

	// Generate JWT token
	token, err := middleware.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate token",
		})
		return
	}

	// Return response
	response := LoginResponse{
		User:  user.ToResponse(),
		Token: token,
	}

	c.JSON(http.StatusOK, response)
}

// GetCurrentUser returns the current authenticated user
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user.ToResponse(),
	})
}

// RefreshToken generates a new token for the current user
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Generate new JWT token
	token, err := middleware.GenerateJWT(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token": token,
		"user":  user.ToResponse(),
	})
}

// Logout handles user logout (client-side token removal)
func (h *AuthHandler) Logout(c *gin.Context) {
	// Since we're using stateless JWT tokens, logout is handled client-side
	// by removing the token from storage. We just return a success message.
	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

// ValidateToken validates a JWT token without requiring authentication middleware
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// Get token from request body or query parameter
	var tokenReq struct {
		Token string `json:"token" form:"token"`
	}
	
	if err := c.ShouldBind(&tokenReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Token required",
		})
		return
	}

	// Validate token
	claims, err := middleware.ValidateJWT(tokenReq.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "Invalid or expired token",
		})
		return
	}

	// Get user to ensure they still exist
	user, err := models.GetUserByID(h.db, claims.UserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "User not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid": true,
		"user":  user.ToResponse(),
	})
}
