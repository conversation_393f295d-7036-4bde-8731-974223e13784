package services

import (
	"log"
	"time"
)

// BackupScheduler handles automatic backup scheduling
type BackupScheduler struct {
	backupService *BackupService
	stopChan      chan bool
	isRunning     bool
}

// ScheduleConfig defines backup schedule configuration
type ScheduleConfig struct {
	DailyBackupTime   string // Format: "15:04" (24-hour format)
	WeeklyBackupDay   int    // 0=Sunday, 1=Monday, etc.
	MonthlyBackupDay  int    // Day of month (1-28)
	EnableDailyBackup bool
	EnableWeeklyBackup bool
	EnableMonthlyBackup bool
}

// NewBackupScheduler creates a new backup scheduler
func NewBackupScheduler(backupService *BackupService) *BackupScheduler {
	return &BackupScheduler{
		backupService: backupService,
		stopChan:      make(chan bool),
		isRunning:     false,
	}
}

// Start begins the backup scheduler with the given configuration
func (bs *BackupScheduler) Start(config ScheduleConfig) {
	if bs.isRunning {
		log.Println("Backup scheduler is already running")
		return
	}

	bs.isRunning = true
	log.Println("Starting backup scheduler...")

	go bs.run(config)
}

// Stop stops the backup scheduler
func (bs *BackupScheduler) Stop() {
	if !bs.isRunning {
		return
	}

	log.Println("Stopping backup scheduler...")
	bs.stopChan <- true
	bs.isRunning = false
}

// IsRunning returns whether the scheduler is currently running
func (bs *BackupScheduler) IsRunning() bool {
	return bs.isRunning
}

// run is the main scheduler loop
func (bs *BackupScheduler) run(config ScheduleConfig) {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	log.Printf("Backup scheduler started with config: Daily=%v, Weekly=%v, Monthly=%v", 
		config.EnableDailyBackup, config.EnableWeeklyBackup, config.EnableMonthlyBackup)

	// Track last backup times to avoid duplicates
	lastDailyBackup := time.Time{}
	lastWeeklyBackup := time.Time{}
	lastMonthlyBackup := time.Time{}

	for {
		select {
		case <-bs.stopChan:
			log.Println("Backup scheduler stopped")
			return
		case now := <-ticker.C:
			// Check for daily backup
			if config.EnableDailyBackup {
				if bs.shouldRunDailyBackup(now, config.DailyBackupTime, lastDailyBackup) {
					go func() {
						if _, err := bs.backupService.CreateBackup("daily"); err != nil {
							log.Printf("Failed to create daily backup: %v", err)
						} else {
							log.Println("Daily backup completed successfully")
						}
					}()
					lastDailyBackup = now
				}
			}

			// Check for weekly backup
			if config.EnableWeeklyBackup {
				if bs.shouldRunWeeklyBackup(now, config.WeeklyBackupDay, lastWeeklyBackup) {
					go func() {
						if _, err := bs.backupService.CreateBackup("weekly"); err != nil {
							log.Printf("Failed to create weekly backup: %v", err)
						} else {
							log.Println("Weekly backup completed successfully")
						}
					}()
					lastWeeklyBackup = now
				}
			}

			// Check for monthly backup
			if config.EnableMonthlyBackup {
				if bs.shouldRunMonthlyBackup(now, config.MonthlyBackupDay, lastMonthlyBackup) {
					go func() {
						if _, err := bs.backupService.CreateBackup("monthly"); err != nil {
							log.Printf("Failed to create monthly backup: %v", err)
						} else {
							log.Println("Monthly backup completed successfully")
						}
					}()
					lastMonthlyBackup = now
				}
			}
		}
	}
}

// shouldRunDailyBackup checks if it's time for a daily backup
func (bs *BackupScheduler) shouldRunDailyBackup(now time.Time, backupTime string, lastBackup time.Time) bool {
	// Parse the backup time
	targetTime, err := time.Parse("15:04", backupTime)
	if err != nil {
		log.Printf("Invalid daily backup time format: %s", backupTime)
		return false
	}

	// Create target time for today
	today := time.Date(now.Year(), now.Month(), now.Day(), 
		targetTime.Hour(), targetTime.Minute(), 0, 0, now.Location())

	// Check if we've passed the target time today and haven't backed up today
	if now.After(today) && (lastBackup.IsZero() || !isSameDay(lastBackup, now)) {
		return true
	}

	return false
}

// shouldRunWeeklyBackup checks if it's time for a weekly backup
func (bs *BackupScheduler) shouldRunWeeklyBackup(now time.Time, weekday int, lastBackup time.Time) bool {
	// Check if today is the target weekday
	if int(now.Weekday()) != weekday {
		return false
	}

	// Check if we haven't backed up this week
	if lastBackup.IsZero() || !isSameWeek(lastBackup, now) {
		return true
	}

	return false
}

// shouldRunMonthlyBackup checks if it's time for a monthly backup
func (bs *BackupScheduler) shouldRunMonthlyBackup(now time.Time, dayOfMonth int, lastBackup time.Time) bool {
	// Check if today is the target day of month
	if now.Day() != dayOfMonth {
		return false
	}

	// Check if we haven't backed up this month
	if lastBackup.IsZero() || !isSameMonth(lastBackup, now) {
		return true
	}

	return false
}

// Helper functions for date comparisons

func isSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

func isSameWeek(t1, t2 time.Time) bool {
	y1, w1 := t1.ISOWeek()
	y2, w2 := t2.ISOWeek()
	return y1 == y2 && w1 == w2
}

func isSameMonth(t1, t2 time.Time) bool {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return y1 == y2 && m1 == m2
}

// GetDefaultConfig returns a sensible default configuration
func GetDefaultScheduleConfig() ScheduleConfig {
	return ScheduleConfig{
		DailyBackupTime:     "02:00", // 2 AM
		WeeklyBackupDay:     0,       // Sunday
		MonthlyBackupDay:    1,       // 1st of month
		EnableDailyBackup:   true,
		EnableWeeklyBackup:  true,
		EnableMonthlyBackup: true,
	}
}

// CreateImmediateBackup creates a backup immediately (for testing or manual triggers)
func (bs *BackupScheduler) CreateImmediateBackup(backupType string) (*BackupInfo, error) {
	log.Printf("Creating immediate %s backup...", backupType)
	return bs.backupService.CreateBackup(backupType)
}
