<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Save-It</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>Save-It</strong>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/login">Login</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title text-center mb-4">Create Account</h2>
                        
                        <div id="error-message" class="alert alert-danger d-none"></div>
                        
                        <form id="register-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       minlength="3" maxlength="50" required>
                                <div class="form-text">At least 3 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <div class="form-text">At least 6 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm-password" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="confirm-password" 
                                       name="confirm-password" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="register-btn">
                                    <span class="spinner-border spinner-border-sm d-none" id="register-spinner"></span>
                                    Create Account
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <p class="mb-0">Already have an account? <a href="/login">Login here</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const registerBtn = document.getElementById('register-btn');
            const spinner = document.getElementById('register-spinner');
            const errorDiv = document.getElementById('error-message');
            
            // Reset error state
            errorDiv.classList.add('d-none');
            
            const formData = new FormData(this);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm-password');
            
            // Validate passwords match
            if (password !== confirmPassword) {
                errorDiv.textContent = 'Passwords do not match';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // Show loading state
            registerBtn.disabled = true;
            spinner.classList.remove('d-none');
            
            const data = {
                username: formData.get('username'),
                password: password
            };
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Store token and redirect
                    localStorage.setItem('auth_token', result.token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    window.location.href = '/saves';
                } else {
                    throw new Error(result.error || 'Registration failed');
                }
            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                // Reset loading state
                registerBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
