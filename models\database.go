package models

import (
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase initializes the database connection and runs migrations
func InitDatabase(dbPath string) (*gorm.DB, error) {
	// Configure GORM logger
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}
	
	// Open database connection
	db, err := gorm.Open(sqlite.Open(dbPath), config)
	if err != nil {
		return nil, err
	}
	
	// Run auto migrations
	if err := runMigrations(db); err != nil {
		return nil, err
	}
	
	log.Println("Database initialized successfully")
	return db, nil
}

// runMigrations runs all database migrations
func runMigrations(db *gorm.DB) error {
	log.Println("Running database migrations...")
	
	// Auto-migrate all models
	err := db.AutoMigrate(
		&User{},
		&SavedItem{},
	)
	
	if err != nil {
		return err
	}
	
	// Create indexes for better performance
	if err := createIndexes(db); err != nil {
		return err
	}
	
	log.Println("Database migrations completed successfully")
	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes(db *gorm.DB) error {
	// Index on saved_items.user_id for faster user queries
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_saved_items_user_id ON saved_items(user_id)").Error; err != nil {
		return err
	}
	
	// Index on saved_items.url for duplicate checking
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_saved_items_url ON saved_items(url)").Error; err != nil {
		return err
	}
	
	// Index on saved_items.status for filtering
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_saved_items_status ON saved_items(status)").Error; err != nil {
		return err
	}
	
	// Index on saved_items.is_favorite for filtering
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_saved_items_favorite ON saved_items(is_favorite)").Error; err != nil {
		return err
	}
	
	// Composite index for user_id + url for faster duplicate checking
	if err := db.Exec("CREATE UNIQUE INDEX IF NOT EXISTS idx_saved_items_user_url ON saved_items(user_id, url)").Error; err != nil {
		return err
	}
	
	// Index on saved_items.created_at for ordering
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_saved_items_created_at ON saved_items(created_at)").Error; err != nil {
		return err
	}
	
	log.Println("Database indexes created successfully")
	return nil
}

// GetDatabaseStats returns basic statistics about the database
func GetDatabaseStats(db *gorm.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Count users
	var userCount int64
	if err := db.Model(&User{}).Count(&userCount).Error; err != nil {
		return nil, err
	}
	stats["users"] = userCount
	
	// Count saved items
	var itemCount int64
	if err := db.Model(&SavedItem{}).Count(&itemCount).Error; err != nil {
		return nil, err
	}
	stats["saved_items"] = itemCount
	
	// Count by status
	var unreadCount, archivedCount int64
	db.Model(&SavedItem{}).Where("status = ?", "UNREAD").Count(&unreadCount)
	db.Model(&SavedItem{}).Where("status = ?", "ARCHIVED").Count(&archivedCount)
	
	stats["unread_items"] = unreadCount
	stats["archived_items"] = archivedCount
	
	// Count favorites
	var favoriteCount int64
	db.Model(&SavedItem{}).Where("is_favorite = ?", true).Count(&favoriteCount)
	stats["favorite_items"] = favoriteCount
	
	return stats, nil
}
