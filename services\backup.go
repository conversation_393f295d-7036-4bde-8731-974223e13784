package services

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"gorm.io/gorm"
)

// BackupService handles database backup operations
type BackupService struct {
	db         *gorm.DB
	backupDir  string
	maxBackups int
}

// BackupInfo represents information about a backup file
type BackupInfo struct {
	Filename    string    `json:"filename"`
	Path        string    `json:"path"`
	Size        int64     `json:"size"`
	CreatedAt   time.Time `json:"created_at"`
	Type        string    `json:"type"` // manual, daily, weekly, monthly
	IsCorrupted bool      `json:"is_corrupted"`
}

// NewBackupService creates a new backup service instance
func NewBackupService(db *gorm.DB, backupDir string, maxBackups int) *BackupService {
	if maxBackups <= 0 {
		maxBackups = 30 // Default to keeping 30 backups
	}

	return &BackupService{
		db:         db,
		backupDir:  backupDir,
		maxBackups: maxBackups,
	}
}

// Initialize creates the backup directory if it doesn't exist
func (bs *BackupService) Initialize() error {
	if err := os.MkdirAll(bs.backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	log.Printf("Backup service initialized with directory: %s", bs.backupDir)
	return nil
}

// CreateBackup creates a backup of the database
func (bs *BackupService) CreateBackup(backupType string) (*BackupInfo, error) {
	// Get the underlying SQLite database file path
	sqlDB, err := bs.db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying database: %w", err)
	}

	// Close any active connections temporarily for a clean backup
	sqlDB.Close()

	// Reopen the database connection after backup
	defer func() {
		// Note: In a real application, you'd want to handle this more gracefully
		// This is a simplified approach for demonstration
	}()

	// Generate backup filename with timestamp
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := fmt.Sprintf("save-it_%s_%s.db", backupType, timestamp)
	backupPath := filepath.Join(bs.backupDir, filename)

	// Copy the database file
	if err := bs.copyFile("save-it.db", backupPath); err != nil {
		return nil, fmt.Errorf("failed to copy database file: %w", err)
	}

	// Get file info
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get backup file info: %w", err)
	}

	backup := &BackupInfo{
		Filename:  filename,
		Path:      backupPath,
		Size:      fileInfo.Size(),
		CreatedAt: time.Now(),
		Type:      backupType,
	}

	// Verify backup integrity
	backup.IsCorrupted = !bs.verifyBackupIntegrity(backupPath)

	log.Printf("Created %s backup: %s (%.2f KB)", backupType, filename, float64(backup.Size)/1024)

	// Clean up old backups
	if err := bs.cleanupOldBackups(backupType); err != nil {
		log.Printf("Warning: Failed to cleanup old backups: %v", err)
	}

	return backup, nil
}

// copyFile copies a file from src to dst
func (bs *BackupService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// Sync to ensure data is written to disk
	return destFile.Sync()
}

// verifyBackupIntegrity checks if a backup file is valid
func (bs *BackupService) verifyBackupIntegrity(backupPath string) bool {
	// Simple integrity check - verify file exists and has content
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		return false
	}

	// Check if file has reasonable size (at least 1KB for SQLite header)
	if fileInfo.Size() < 1024 {
		return false
	}

	// TODO: Add more sophisticated integrity checks like:
	// - SQLite file format validation
	// - Database schema verification
	// - Sample query execution

	return true
}

// ListBackups returns a list of all available backups
func (bs *BackupService) ListBackups() ([]*BackupInfo, error) {
	files, err := os.ReadDir(bs.backupDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read backup directory: %w", err)
	}

	var backups []*BackupInfo

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".db") {
			continue
		}

		filePath := filepath.Join(bs.backupDir, file.Name())
		fileInfo, err := file.Info()
		if err != nil {
			continue
		}

		// Parse backup type from filename
		backupType := "manual"
		if strings.Contains(file.Name(), "_daily_") {
			backupType = "daily"
		} else if strings.Contains(file.Name(), "_weekly_") {
			backupType = "weekly"
		} else if strings.Contains(file.Name(), "_monthly_") {
			backupType = "monthly"
		}

		backup := &BackupInfo{
			Filename:    file.Name(),
			Path:        filePath,
			Size:        fileInfo.Size(),
			CreatedAt:   fileInfo.ModTime(),
			Type:        backupType,
			IsCorrupted: !bs.verifyBackupIntegrity(filePath),
		}

		backups = append(backups, backup)
	}

	// Sort by creation time (newest first)
	sort.Slice(backups, func(i, j int) bool {
		return backups[i].CreatedAt.After(backups[j].CreatedAt)
	})

	return backups, nil
}

// cleanupOldBackups removes old backup files to maintain the maximum count
func (bs *BackupService) cleanupOldBackups(backupType string) error {
	backups, err := bs.ListBackups()
	if err != nil {
		return err
	}

	// Filter backups by type
	var typeBackups []*BackupInfo
	for _, backup := range backups {
		if backup.Type == backupType {
			typeBackups = append(typeBackups, backup)
		}
	}

	// Remove excess backups
	if len(typeBackups) > bs.maxBackups {
		for i := bs.maxBackups; i < len(typeBackups); i++ {
			if err := os.Remove(typeBackups[i].Path); err != nil {
				log.Printf("Warning: Failed to remove old backup %s: %v", typeBackups[i].Filename, err)
			} else {
				log.Printf("Removed old backup: %s", typeBackups[i].Filename)
			}
		}
	}

	return nil
}

// RestoreBackup restores the database from a backup file
func (bs *BackupService) RestoreBackup(backupPath string) error {
	// Verify backup exists and is valid
	if !bs.verifyBackupIntegrity(backupPath) {
		return fmt.Errorf("backup file is corrupted or invalid")
	}

	// Create a backup of current database before restoration
	currentBackup, err := bs.CreateBackup("pre_restore")
	if err != nil {
		log.Printf("Warning: Failed to create pre-restore backup: %v", err)
	} else {
		log.Printf("Created pre-restore backup: %s", currentBackup.Filename)
	}

	// Copy backup file to main database location
	if err := bs.copyFile(backupPath, "save-it.db"); err != nil {
		return fmt.Errorf("failed to restore backup: %w", err)
	}

	log.Printf("Successfully restored database from backup: %s", filepath.Base(backupPath))
	return nil
}

// GetBackupStats returns statistics about backups
func (bs *BackupService) GetBackupStats() (map[string]interface{}, error) {
	backups, err := bs.ListBackups()
	if err != nil {
		return nil, err
	}

	stats := make(map[string]interface{})
	stats["total_backups"] = len(backups)
	stats["backup_directory"] = bs.backupDir

	// Count by type
	typeCounts := make(map[string]int)
	var totalSize int64
	corruptedCount := 0

	for _, backup := range backups {
		typeCounts[backup.Type]++
		totalSize += backup.Size
		if backup.IsCorrupted {
			corruptedCount++
		}
	}

	stats["by_type"] = typeCounts
	stats["total_size_bytes"] = totalSize
	stats["total_size_mb"] = float64(totalSize) / (1024 * 1024)
	stats["corrupted_backups"] = corruptedCount

	if len(backups) > 0 {
		stats["latest_backup"] = backups[0]
		stats["oldest_backup"] = backups[len(backups)-1]
	}

	return stats, nil
}

// DeleteBackup deletes a specific backup file
func (bs *BackupService) DeleteBackup(backupPath string) error {
	if err := os.Remove(backupPath); err != nil {
		return fmt.Errorf("failed to delete backup file: %w", err)
	}

	log.Printf("Deleted backup file: %s", filepath.Base(backupPath))
	return nil
}
