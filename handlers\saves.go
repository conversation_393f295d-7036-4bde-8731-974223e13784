package handlers

import (
	"net/http"
	"strconv"

	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SavesHandler handles saved items requests
type SavesHandler struct {
	db                *gorm.DB
	metadataExtractor *MetadataExtractor
}

// NewSavesHandler creates a new saves handler
func NewSavesHandler(db *gorm.DB) *SavesHandler {
	return &SavesHandler{
		db:                db,
		metadataExtractor: NewMetadataExtractor(db),
	}
}

// SaveURL saves a new URL for the authenticated user
func (h *SavesHandler) SaveURL(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	var req models.SaveItemRequest

	// Bind JSON request
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create saved item
	savedItem, err := models.CreateSavedItem(h.db, userID, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to save URL",
			"details": err.Error(),
		})
		return
	}

	// Extract metadata in background
	h.metadataExtractor.ExtractMetadataAsync(savedItem)

	c.JSON(http.StatusCreated, gin.H{
		"message": "URL saved successfully",
		"item":    savedItem.ToResponse(),
	})
}

// GetSavedItems retrieves all saved items for the authenticated user
func (h *SavesHandler) GetSavedItems(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Parse query parameters
	query := c.Query("q")
	status := c.Query("status")
	favoriteStr := c.Query("favorite")
	sortBy := c.DefaultQuery("sort", "created_at_desc")
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")

	// Parse pagination
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse favorite filter
	var favorite *bool
	if favoriteStr != "" {
		if favoriteStr == "true" {
			f := true
			favorite = &f
		} else if favoriteStr == "false" {
			f := false
			favorite = &f
		}
	}

	// Get saved items
	items, total, err := models.GetSavedItemsByUser(h.db, userID, query, status, favorite, sortBy, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve saved items",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	itemResponses := make([]models.SavedItemResponse, len(items))
	for i, item := range items {
		itemResponses[i] = item.ToResponse()
	}

	response := models.SavedItemsResponse{
		Items: itemResponses,
		Total: total,
		Page:  page,
		Limit: limit,
	}

	c.JSON(http.StatusOK, response)
}

// GetSavedItem retrieves a specific saved item by ID
func (h *SavesHandler) GetSavedItem(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Parse item ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid item ID",
		})
		return
	}

	// Get saved item
	item, err := models.GetSavedItemByID(h.db, uint(id), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Saved item not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to retrieve saved item",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"item": item.ToResponse(),
	})
}

// UpdateSavedItem updates a saved item
func (h *SavesHandler) UpdateSavedItem(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Parse item ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid item ID",
		})
		return
	}

	var req models.UpdateItemRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Update saved item
	item, err := models.UpdateSavedItem(h.db, uint(id), userID, req)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Saved item not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to update saved item",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Item updated successfully",
		"item":    item.ToResponse(),
	})
}

// DeleteSavedItem deletes a saved item
func (h *SavesHandler) DeleteSavedItem(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Parse item ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid item ID",
		})
		return
	}

	// Delete saved item
	err = models.DeleteSavedItem(h.db, uint(id), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Saved item not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to delete saved item",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Item deleted successfully",
	})
}
