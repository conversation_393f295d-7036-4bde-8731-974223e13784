package handlers

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"save-it/models"

	"github.com/PuerkitoBio/goquery"
	"gorm.io/gorm"
)

// MetadataExtractor handles URL metadata extraction
type MetadataExtractor struct {
	db     *gorm.DB
	client *http.Client
}

// NewMetadataExtractor creates a new metadata extractor
func NewMetadataExtractor(db *gorm.DB) *MetadataExtractor {
	return &MetadataExtractor{
		db: db,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// URLMetadata represents extracted metadata from a URL
type URLMetadata struct {
	Title       string
	Description string
	ImageURL    string
	SiteName    string
	URL         string
}

// ExtractMetadata extracts metadata from a URL
func (m *MetadataExtractor) ExtractMetadata(targetURL string) (*URLMetadata, error) {
	// Validate URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %v", err)
	}

	// Ensure URL has a scheme
	if parsedURL.Scheme == "" {
		targetURL = "https://" + targetURL
		parsedURL, err = url.Parse(targetURL)
		if err != nil {
			return nil, fmt.Errorf("invalid URL after adding scheme: %v", err)
		}
	}

	// Create request with proper headers
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set user agent to avoid being blocked
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")

	// Make request
	resp, err := m.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch URL: %v", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	// Check content type
	contentType := resp.Header.Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentType), "text/html") {
		return nil, fmt.Errorf("not an HTML page: %s", contentType)
	}

	// Limit response size to prevent abuse
	limitedReader := io.LimitReader(resp.Body, 1024*1024) // 1MB limit

	// Parse HTML
	doc, err := goquery.NewDocumentFromReader(limitedReader)
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %v", err)
	}

	metadata := &URLMetadata{
		URL: targetURL,
	}

	// Extract title
	metadata.Title = m.extractTitle(doc)

	// Extract description
	metadata.Description = m.extractDescription(doc)

	// Extract image
	metadata.ImageURL = m.extractImage(doc, parsedURL)

	// Extract site name
	metadata.SiteName = m.extractSiteName(doc, parsedURL)

	return metadata, nil
}

// extractTitle extracts the page title
func (m *MetadataExtractor) extractTitle(doc *goquery.Document) string {
	// Try Open Graph title first
	if title := doc.Find("meta[property='og:title']").AttrOr("content", ""); title != "" {
		return strings.TrimSpace(title)
	}

	// Try Twitter title
	if title := doc.Find("meta[name='twitter:title']").AttrOr("content", ""); title != "" {
		return strings.TrimSpace(title)
	}

	// Fall back to HTML title
	if title := doc.Find("title").Text(); title != "" {
		return strings.TrimSpace(title)
	}

	return ""
}

// extractDescription extracts the page description
func (m *MetadataExtractor) extractDescription(doc *goquery.Document) string {
	// Try Open Graph description first
	if desc := doc.Find("meta[property='og:description']").AttrOr("content", ""); desc != "" {
		return strings.TrimSpace(desc)
	}

	// Try Twitter description
	if desc := doc.Find("meta[name='twitter:description']").AttrOr("content", ""); desc != "" {
		return strings.TrimSpace(desc)
	}

	// Try meta description
	if desc := doc.Find("meta[name='description']").AttrOr("content", ""); desc != "" {
		return strings.TrimSpace(desc)
	}

	// Try to extract from first paragraph
	if desc := doc.Find("p").First().Text(); desc != "" {
		desc = strings.TrimSpace(desc)
		if len(desc) > 200 {
			desc = desc[:200] + "..."
		}
		return desc
	}

	return ""
}

// extractImage extracts the main image URL
func (m *MetadataExtractor) extractImage(doc *goquery.Document, baseURL *url.URL) string {
	// Try Open Graph image first
	if img := doc.Find("meta[property='og:image']").AttrOr("content", ""); img != "" {
		return m.resolveURL(img, baseURL)
	}

	// Try Twitter image
	if img := doc.Find("meta[name='twitter:image']").AttrOr("content", ""); img != "" {
		return m.resolveURL(img, baseURL)
	}

	// Try to find the first reasonable image
	var imageURL string
	doc.Find("img").EachWithBreak(func(i int, s *goquery.Selection) bool {
		src := s.AttrOr("src", "")
		if src == "" {
			return true // continue
		}

		// Skip small images, icons, etc.
		width := s.AttrOr("width", "")
		height := s.AttrOr("height", "")
		
		// Skip if explicitly small
		if (width != "" && width < "100") || (height != "" && height < "100") {
			return true // continue
		}

		// Skip common icon/logo patterns
		if strings.Contains(strings.ToLower(src), "icon") ||
			strings.Contains(strings.ToLower(src), "logo") ||
			strings.Contains(strings.ToLower(src), "avatar") {
			return true // continue
		}

		imageURL = m.resolveURL(src, baseURL)
		return false // break
	})

	return imageURL
}

// extractSiteName extracts the site name
func (m *MetadataExtractor) extractSiteName(doc *goquery.Document, baseURL *url.URL) string {
	// Try Open Graph site name
	if siteName := doc.Find("meta[property='og:site_name']").AttrOr("content", ""); siteName != "" {
		return strings.TrimSpace(siteName)
	}

	// Fall back to hostname
	return baseURL.Hostname()
}

// resolveURL resolves a relative URL against a base URL
func (m *MetadataExtractor) resolveURL(href string, baseURL *url.URL) string {
	if href == "" {
		return ""
	}

	// Parse the href
	parsedHref, err := url.Parse(href)
	if err != nil {
		return ""
	}

	// Resolve against base URL
	resolved := baseURL.ResolveReference(parsedHref)
	return resolved.String()
}

// ExtractAndUpdateMetadata extracts metadata for a saved item and updates it in the database
func (m *MetadataExtractor) ExtractAndUpdateMetadata(savedItem *models.SavedItem) error {
	metadata, err := m.ExtractMetadata(savedItem.URL)
	if err != nil {
		// Log error but don't fail the save operation
		fmt.Printf("Failed to extract metadata for %s: %v\n", savedItem.URL, err)
		return nil
	}

	// Update the saved item with metadata
	savedItem.SetMetadata(metadata.Title, metadata.Description, metadata.ImageURL)

	// Save to database
	return m.db.Save(savedItem).Error
}

// ExtractMetadataAsync extracts metadata in the background
func (m *MetadataExtractor) ExtractMetadataAsync(savedItem *models.SavedItem) {
	go func() {
		if err := m.ExtractAndUpdateMetadata(savedItem); err != nil {
			fmt.Printf("Failed to update metadata for item %d: %v\n", savedItem.ID, err)
		}
	}()
}
