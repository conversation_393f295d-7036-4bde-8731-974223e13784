// Save-It Application JavaScript

// Theme management
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.createThemeToggle();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        this.updateToggleIcon();
    }

    createThemeToggle() {
        const toggle = document.createElement('button');
        toggle.className = 'theme-toggle';
        toggle.innerHTML = this.getToggleIcon();
        toggle.title = `Switch to ${this.currentTheme === 'light' ? 'dark' : 'light'} theme`;
        toggle.addEventListener('click', () => this.toggleTheme());
        document.body.appendChild(toggle);
    }

    getToggleIcon() {
        // Use SVG icons for better visibility and consistency
        if (this.currentTheme === 'light') {
            // Moon icon for switching to dark mode
            return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
            </svg>`;
        } else {
            // Sun icon for switching to light mode
            return `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="5"/>
                <line x1="12" y1="1" x2="12" y2="3"/>
                <line x1="12" y1="21" x2="12" y2="23"/>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                <line x1="1" y1="12" x2="3" y2="12"/>
                <line x1="21" y1="12" x2="23" y2="12"/>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
            </svg>`;
        }
    }

    updateToggleIcon() {
        const toggle = document.querySelector('.theme-toggle');
        if (toggle) {
            toggle.innerHTML = this.getToggleIcon();
            toggle.title = `Switch to ${this.currentTheme === 'light' ? 'dark' : 'light'} theme`;
        }
    }
}

// API helper functions
class ApiClient {
    constructor() {
        this.baseUrl = window.location.origin;
        this.token = localStorage.getItem('auth_token');
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (this.token) {
            config.headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    async get(endpoint) {
        return this.request(endpoint);
    }

    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    setToken(token) {
        this.token = token;
        localStorage.setItem('auth_token', token);
    }

    clearToken() {
        this.token = null;
        localStorage.removeItem('auth_token');
    }
}

// Search and filter functionality
class SearchManager {
    constructor() {
        this.searchInput = document.getElementById('search-input');
        this.statusFilter = document.getElementById('status-filter');
        this.favoriteFilter = document.getElementById('favorite-filter');
        this.init();
    }

    init() {
        if (this.searchInput) {
            this.searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }

        if (this.statusFilter) {
            this.statusFilter.addEventListener('change', this.handleFilter.bind(this));
        }

        if (this.favoriteFilter) {
            this.favoriteFilter.addEventListener('change', this.handleFilter.bind(this));
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    handleSearch() {
        this.performSearch();
    }

    handleFilter() {
        this.performSearch();
    }

    async performSearch() {
        const query = this.searchInput?.value || '';
        const status = this.statusFilter?.value || '';
        const favorite = this.favoriteFilter?.value || '';

        const params = new URLSearchParams();
        if (query) params.append('q', query);
        if (status) params.append('status', status);
        if (favorite) params.append('favorite', favorite);

        // Add sorting and pagination parameters if they exist globally
        if (window.currentSort) params.append('sort', window.currentSort);
        if (window.currentPage) params.append('page', window.currentPage.toString());
        if (window.currentLimit) params.append('limit', window.currentLimit.toString());

        try {
            const api = new ApiClient();
            const results = await api.get(`/api/saves?${params.toString()}`);

            // Handle both old format (array) and new format (object with items)
            if (Array.isArray(results)) {
                this.displayResults(results);
            } else if (results.items) {
                this.displayResults(results.items);
                // Update results count if available
                const resultsCount = document.getElementById('results-count');
                if (resultsCount && results.total !== undefined) {
                    resultsCount.textContent = `${results.total} items found`;
                }
            } else {
                this.displayResults([]);
            }
        } catch (error) {
            console.error('Search failed:', error);
            this.showError('Search failed. Please try again.');
        }
    }

    displayResults(results) {
        const container = document.getElementById('saves-container');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">No saved items found.</div>';
            return;
        }

        container.innerHTML = results.map(item => this.renderSavedItem(item)).join('');
    }

    renderSavedItem(item) {
        return `
            <div class="saved-item" data-id="${item.id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h5 class="saved-item-title">
                            <a href="${item.url}" target="_blank" class="text-decoration-none">
                                ${item.title || item.url}
                            </a>
                        </h5>
                        <a href="${item.url}" class="saved-item-url" target="_blank">${item.url}</a>
                        ${item.excerpt ? `<p class="saved-item-excerpt">${item.excerpt}</p>` : ''}
                        <div class="saved-item-meta">
                            <span class="status-badge badge bg-secondary">${item.status}</span>
                            <span class="text-muted ms-2">${new Date(item.created_at).toLocaleDateString()}</span>
                        </div>
                    </div>
                    <div class="ms-3">
                        <button class="btn-favorite ${item.is_favorite ? 'active' : ''}"
                                onclick="toggleFavorite(${item.id})">
                            ⭐
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-2"
                                onclick="deleteSavedItem(${item.id})">
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    showError(message) {
        const container = document.getElementById('saves-container');
        if (container) {
            container.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        }
    }
}

// Utility functions
function showToast(message, type = 'info') {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Failed to copy to clipboard', 'danger');
    });
}

// Global functions for inline event handlers
window.toggleFavorite = async function(id) {
    try {
        const api = new ApiClient();
        await api.put(`/api/save/${id}`, { toggle_favorite: true });
        showToast('Favorite status updated', 'success');
        // Refresh the current view
        if (window.searchManager) {
            window.searchManager.performSearch();
        }
    } catch (error) {
        showToast('Failed to update favorite status', 'danger');
    }
};

window.deleteSavedItem = async function(id) {
    if (!confirm('Are you sure you want to delete this saved item?')) {
        return;
    }

    try {
        const api = new ApiClient();
        await api.delete(`/api/save/${id}`);
        showToast('Item deleted successfully', 'success');
        // Refresh the current view
        if (window.searchManager) {
            window.searchManager.performSearch();
        }
    } catch (error) {
        showToast('Failed to delete item', 'danger');
    }
};

// Global functions for user management
window.changePassword = async function() {
    const currentPasswordInput = document.getElementById('current-password');
    const newPasswordInput = document.getElementById('new-password');
    const confirmPasswordInput = document.getElementById('confirm-password');
    const submitBtn = document.getElementById('change-password-submit-btn');
    const spinner = document.getElementById('change-password-spinner');
    const errorDiv = document.getElementById('change-password-error');

    const currentPassword = currentPasswordInput.value.trim();
    const newPassword = newPasswordInput.value.trim();
    const confirmPassword = confirmPasswordInput.value.trim();

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
        errorDiv.textContent = 'All fields are required';
        errorDiv.classList.remove('d-none');
        return;
    }

    if (newPassword.length < 6) {
        errorDiv.textContent = 'New password must be at least 6 characters long';
        errorDiv.classList.remove('d-none');
        return;
    }

    if (newPassword !== confirmPassword) {
        errorDiv.textContent = 'New passwords do not match';
        errorDiv.classList.remove('d-none');
        return;
    }

    try {
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');
        errorDiv.classList.add('d-none');

        const api = new ApiClient();
        await api.post('/api/change-password', {
            current_password: currentPassword,
            new_password: newPassword
        });

        // Close modal and show success
        const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
        modal.hide();

        // Clear form
        currentPasswordInput.value = '';
        newPasswordInput.value = '';
        confirmPasswordInput.value = '';

        showToast('Password changed successfully!', 'success');

    } catch (error) {
        errorDiv.textContent = error.message;
        errorDiv.classList.remove('d-none');
    } finally {
        submitBtn.disabled = false;
        spinner.classList.add('d-none');
    }
};

window.clearAllSaves = async function() {
    const submitBtn = document.getElementById('clear-all-saves-submit-btn');
    const spinner = document.getElementById('clear-all-saves-spinner');
    const errorDiv = document.getElementById('clear-all-saves-error');

    try {
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');
        errorDiv.classList.add('d-none');

        const api = new ApiClient();
        const response = await api.delete('/api/saves/clear-all');

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('clearAllSavesModal'));
        modal.hide();

        // Reset checkbox
        document.getElementById('confirm-clear-all').checked = false;

        showToast(`All saves cleared successfully! (${response.deleted_count} items deleted)`, 'success');

        // Reload saves if on saves page
        if (typeof loadSaves === 'function') {
            loadSaves();
        }

    } catch (error) {
        errorDiv.textContent = error.message;
        errorDiv.classList.remove('d-none');
    } finally {
        submitBtn.disabled = false;
        spinner.classList.add('d-none');
    }
};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme manager
    window.themeManager = new ThemeManager();

    // Initialize search manager if on saves page
    if (document.getElementById('search-input')) {
        window.searchManager = new SearchManager();
    }

    // Initialize API client
    window.api = new ApiClient();
});
