<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Saves - Save-It</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>Save-It</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/saves">My Saves</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/import">Import</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <span id="username-display">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="change-password-btn">Change Password</a></li>
                            <li><a class="dropdown-item text-danger" href="#" id="clear-all-saves-btn">Clear All Saves</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Search and Filter Section -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="search-input" placeholder="Search your saves...">
                        <button class="btn btn-outline-secondary" type="button" id="search-btn">
                            🔍
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="status-filter">
                        <option value="">All Status</option>
                        <option value="UNREAD">Unread</option>
                        <option value="read">Read</option>
                        <option value="ARCHIVED">Archived</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="favorite-filter">
                        <option value="">All Items</option>
                        <option value="true">Favorites Only</option>
                        <option value="false">Non-Favorites</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUrlModal">
                        ➕ Add URL
                    </button>
                </div>
                <div class="col-md-4 text-center">
                    <div class="view-toggle">
                        <button class="btn btn-outline-secondary active" id="view-list" data-view="list">
                            📋 List
                        </button>
                        <button class="btn btn-outline-secondary" id="view-compact" data-view="compact">
                            📄 Compact
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center gap-2">
                        <select class="form-select form-select-sm" id="sort-select" style="width: auto;">
                            <option value="created_at_desc">Originally Saved (Newest)</option>
                            <option value="created_at_asc">Originally Saved (Oldest)</option>
                            <option value="updated_at_desc" selected>Recently Added/Updated</option>
                            <option value="updated_at_asc">Earliest Added/Updated</option>
                            <option value="title_asc">Title A-Z</option>
                            <option value="title_desc">Title Z-A</option>
                        </select>
                        <small class="text-muted" id="results-count">Loading...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading" class="loading">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading your saves...</p>
        </div>

        <!-- Saves Container -->
        <div id="saves-container">
            <!-- Saved items will be loaded here -->
        </div>

        <!-- Pagination -->
        <nav aria-label="Saves pagination" id="pagination-nav" class="d-none mt-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted" id="pagination-info">
                        Showing 1-20 of 100 items
                    </small>
                </div>
                <ul class="pagination mb-0" id="pagination">
                    <!-- Pagination will be generated here -->
                </ul>
                <div>
                    <select class="form-select form-select-sm" id="items-per-page" style="width: auto;">
                        <option value="10">10 per page</option>
                        <option value="20" selected>20 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
            </div>
        </nav>
    </div>

    <!-- Add URL Modal -->
    <div class="modal fade" id="addUrlModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New URL</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="add-url-error" class="alert alert-danger d-none"></div>
                    <form id="add-url-form">
                        <div class="mb-3">
                            <label for="url-input" class="form-label">URL</label>
                            <input type="url" class="form-control" id="url-input" name="url"
                                   placeholder="https://example.com" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-url-btn">
                        <span class="spinner-border spinner-border-sm d-none" id="save-url-spinner"></span>
                        Save URL
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="change-password-error" class="alert alert-danger d-none"></div>
                    <form id="change-password-form">
                        <div class="mb-3">
                            <label for="current-password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current-password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new-password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new-password" name="new_password" required minlength="6">
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm-password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm-password" name="confirm_password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="change-password-submit-btn">
                        <span class="spinner-border spinner-border-sm d-none" id="change-password-spinner"></span>
                        Change Password
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear All Saves Modal -->
    <div class="modal fade" id="clearAllSavesModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">⚠️ Clear All Saves</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="clear-all-saves-error" class="alert alert-danger d-none"></div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> This action cannot be undone!
                    </div>
                    <p>Are you sure you want to delete <strong>ALL</strong> of your saved items? This will permanently remove:</p>
                    <ul>
                        <li>All saved URLs</li>
                        <li>All metadata (titles, excerpts, etc.)</li>
                        <li>All read/unread status</li>
                        <li>All favorite markings</li>
                    </ul>
                    <p class="text-muted">This action is irreversible. Consider exporting your data first if you want to keep a backup.</p>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="confirm-clear-all">
                        <label class="form-check-label" for="confirm-clear-all">
                            I understand this action cannot be undone
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="clear-all-saves-submit-btn" disabled>
                        <span class="spinner-border spinner-border-sm d-none" id="clear-all-saves-spinner"></span>
                        Delete All Saves
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
        // Global variables for pagination and sorting
        let currentPage = 1;
        let currentLimit = 20;
        let currentSort = 'updated_at_desc'; // Default to recently added/updated
        let currentView = localStorage.getItem('saves_view') || 'list';

        // Make variables available globally for SearchManager
        window.currentPage = currentPage;
        window.currentLimit = currentLimit;
        window.currentSort = currentSort;

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Set username in navbar
            if (user.username) {
                document.getElementById('username-display').textContent = user.username;
            }

            // Load saves
            loadSaves();

            // Set up event listeners
            setupEventListeners();
        });

        function setupEventListeners() {
            // Logout
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user');
                window.location.href = '/login';
            });

            // Change Password
            document.getElementById('change-password-btn').addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
                modal.show();
            });

            // Clear All Saves
            document.getElementById('clear-all-saves-btn').addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('clearAllSavesModal'));
                modal.show();
            });

            // Change Password Form
            document.getElementById('change-password-submit-btn').addEventListener('click', changePassword);

            // Clear All Saves Form
            document.getElementById('clear-all-saves-submit-btn').addEventListener('click', clearAllSaves);

            // Enable/disable clear all saves button based on checkbox
            document.getElementById('confirm-clear-all').addEventListener('change', function() {
                document.getElementById('clear-all-saves-submit-btn').disabled = !this.checked;
            });

            // Add URL form
            document.getElementById('save-url-btn').addEventListener('click', saveNewUrl);

            // View toggle
            document.querySelectorAll('.view-toggle .btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.view-toggle .btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    const view = this.dataset.view;
                    localStorage.setItem('saves_view', view);
                    updateItemsView(view);
                });
            });

            // Sort change
            document.getElementById('sort-select').addEventListener('change', function() {
                currentSort = this.value;
                window.currentSort = currentSort;
                currentPage = 1;
                window.currentPage = currentPage;
                loadSaves();
            });

            // Items per page change
            document.getElementById('items-per-page').addEventListener('change', function() {
                currentLimit = parseInt(this.value);
                window.currentLimit = currentLimit;
                currentPage = 1;
                window.currentPage = currentPage;
                loadSaves();
            });

            // Search functionality is handled by SearchManager in app.js
        }

        async function loadSaves() {
            const loading = document.getElementById('loading');
            const container = document.getElementById('saves-container');
            const resultsCount = document.getElementById('results-count');

            try {
                loading.style.display = 'block';
                container.innerHTML = '';

                // Build query parameters
                const params = new URLSearchParams();
                params.append('page', currentPage.toString());
                params.append('limit', currentLimit.toString());
                params.append('sort', currentSort);

                // Add search and filter parameters if they exist
                const searchInput = document.getElementById('search-input');
                const statusFilter = document.getElementById('status-filter');
                const favoriteFilter = document.getElementById('favorite-filter');

                if (searchInput && searchInput.value.trim()) {
                    params.append('q', searchInput.value.trim());
                }
                if (statusFilter && statusFilter.value) {
                    params.append('status', statusFilter.value);
                }
                if (favoriteFilter && favoriteFilter.value) {
                    params.append('favorite', favoriteFilter.value);
                }

                const api = new ApiClient();
                const response = await api.get(`/api/saves?${params.toString()}`);

                loading.style.display = 'none';

                if (response.items && response.items.length > 0) {
                    displaySaves(response.items);
                    resultsCount.textContent = `${response.total} items found`;
                    updatePagination(response);
                } else {
                    container.innerHTML = `
                        <div class="text-center py-5">
                            <h4>No saves yet</h4>
                            <p class="text-muted">Start saving URLs to see them here!</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUrlModal">
                                Add Your First URL
                            </button>
                        </div>
                    `;
                    resultsCount.textContent = '0 items';
                    hidePagination();
                }
            } catch (error) {
                loading.style.display = 'none';
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error Loading Saves</h5>
                        <p>${error.message}</p>
                        <button class="btn btn-outline-danger" onclick="loadSaves()">Try Again</button>
                    </div>
                `;
                resultsCount.textContent = 'Error';
            }
        }

        function displaySaves(items) {
            const container = document.getElementById('saves-container');
            container.innerHTML = items.map(item => {
                const thumbnailHtml = item.image_url ?
                    `<img src="${item.image_url}" alt="Thumbnail" class="saved-item-thumbnail" onerror="this.style.display='none'">` : '';

                // Determine status badge color
                const getStatusBadgeClass = (status) => {
                    switch(status.toLowerCase()) {
                        case 'read': return 'bg-success';
                        case 'archived': return 'bg-info';
                        case 'unread':
                        default: return 'bg-secondary';
                    }
                };

                return `
                    <div class="saved-item ${currentView}" data-id="${item.id}" data-status="${item.status.toLowerCase()}">
                        ${currentView === 'list' && thumbnailHtml ? thumbnailHtml : ''}
                        <div class="saved-item-content">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    ${currentView === 'compact' && thumbnailHtml ? `<div class="d-flex gap-2 align-items-start">${thumbnailHtml}<div class="flex-grow-1">` : '<div>'}
                                        <h5 class="saved-item-title">
                                            <a href="${item.url}" target="_blank" class="text-decoration-none">
                                                ${item.title || item.url}
                                            </a>
                                        </h5>
                                        <a href="${item.url}" class="saved-item-url" target="_blank">${item.url}</a>
                                        ${item.excerpt && currentView !== 'compact' ? `<p class="saved-item-excerpt">${item.excerpt}</p>` : ''}
                                        <div class="saved-item-meta">
                                            <span class="status-badge badge ${getStatusBadgeClass(item.status)}">${item.status.toUpperCase()}</span>
                                            <span class="text-muted ms-2" title="Saved: ${new Date(item.created_at).toLocaleString()}">
                                                ${new Date(item.created_at).toLocaleDateString()}
                                            </span>
                                        </div>
                                        <div class="archive-links mt-2">
                                            <small class="text-muted">
                                                📁 Archive:
                                                <a href="https://archive.ph/${encodeURIComponent(item.url)}" target="_blank" class="archive-link" title="View on Archive.today">Archive.today</a>
                                                <span class="mx-1">•</span>
                                                <a href="https://web.archive.org/web/*/${encodeURIComponent(item.url)}" target="_blank" class="archive-link" title="View on Wayback Machine">Wayback Machine</a>
                                            </small>
                                        </div>
                                    ${currentView === 'compact' && thumbnailHtml ? '</div></div>' : '</div>'}
                                </div>
                                <div class="ms-3 d-flex gap-1">
                                    <button class="btn-read ${item.status === 'READ' ? 'active' : ''}"
                                            onclick="toggleReadStatus(${item.id})"
                                            title="${item.status === 'UNREAD' ? 'Mark as read' : 'Mark as unread'}">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            ${item.status === 'UNREAD' ?
                                                '<path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/><path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>' :
                                                '<path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"/><path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"/><path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.708zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"/>'
                                            }
                                        </svg>
                                    </button>
                                    <button class="btn-archive"
                                            onclick="createArchive('${item.url}')" title="Create new archive">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a2.5 2.5 0 0 1-2.5 2.5h-9A2.5 2.5 0 0 1 1 12.5V5a1 1 0 0 1-1-1V2zm2 3v7.5A1.5 1.5 0 0 0 3.5 14h9a1.5 1.5 0 0 0 1.5-1.5V5H2zm13-3H1v2h14V2zM5 7.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5z"/>
                                        </svg>
                                    </button>
                                    <button class="btn-favorite ${item.is_favorite ? 'active' : ''}"
                                            onclick="toggleFavorite(${item.id})" title="Toggle favorite">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                                        </svg>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteSavedItem(${item.id})" title="Delete">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // Apply current view
            updateItemsView(currentView);
        }

        function updateItemsView(view) {
            currentView = view;
            const items = document.querySelectorAll('.saved-item');
            items.forEach(item => {
                item.className = `saved-item ${view}`;
            });

            // Update view toggle buttons
            document.querySelectorAll('.view-toggle .btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });
        }

        async function saveNewUrl() {
            const urlInput = document.getElementById('url-input');
            const saveBtn = document.getElementById('save-url-btn');
            const spinner = document.getElementById('save-url-spinner');
            const errorDiv = document.getElementById('add-url-error');

            const url = urlInput.value.trim();
            if (!url) return;

            try {
                saveBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                await api.post('/api/save', { url: url });

                // Close modal and reload saves
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUrlModal'));
                modal.hide();
                urlInput.value = '';

                showToast('URL saved successfully!', 'success');
                loadSaves();

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                saveBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        async function changePassword() {
            const currentPasswordInput = document.getElementById('current-password');
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const submitBtn = document.getElementById('change-password-submit-btn');
            const spinner = document.getElementById('change-password-spinner');
            const errorDiv = document.getElementById('change-password-error');

            const currentPassword = currentPasswordInput.value.trim();
            const newPassword = newPasswordInput.value.trim();
            const confirmPassword = confirmPasswordInput.value.trim();

            // Validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                errorDiv.textContent = 'All fields are required';
                errorDiv.classList.remove('d-none');
                return;
            }

            if (newPassword.length < 6) {
                errorDiv.textContent = 'New password must be at least 6 characters long';
                errorDiv.classList.remove('d-none');
                return;
            }

            if (newPassword !== confirmPassword) {
                errorDiv.textContent = 'New passwords do not match';
                errorDiv.classList.remove('d-none');
                return;
            }

            try {
                submitBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                await api.post('/api/change-password', {
                    current_password: currentPassword,
                    new_password: newPassword
                });

                // Close modal and show success
                const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                modal.hide();

                // Clear form
                currentPasswordInput.value = '';
                newPasswordInput.value = '';
                confirmPasswordInput.value = '';

                showToast('Password changed successfully!', 'success');

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                submitBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        async function clearAllSaves() {
            const submitBtn = document.getElementById('clear-all-saves-submit-btn');
            const spinner = document.getElementById('clear-all-saves-spinner');
            const errorDiv = document.getElementById('clear-all-saves-error');

            try {
                submitBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                const response = await api.delete('/api/saves/clear-all');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('clearAllSavesModal'));
                modal.hide();

                // Reset checkbox
                document.getElementById('confirm-clear-all').checked = false;

                showToast(`All saves cleared successfully! (${response.deleted_count} items deleted)`, 'success');

                // Reload saves to show empty state
                loadSaves();

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                submitBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        // Pagination functions
        function updatePagination(response) {
            const paginationNav = document.getElementById('pagination-nav');
            const paginationInfo = document.getElementById('pagination-info');
            const pagination = document.getElementById('pagination');

            // Show pagination if there are items
            if (response.total > 0) {
                paginationNav.classList.remove('d-none');

                // Update pagination info
                const start = (response.page - 1) * response.limit + 1;
                const end = Math.min(response.page * response.limit, response.total);
                paginationInfo.textContent = `Showing ${start}-${end} of ${response.total} items`;

                // Generate pagination buttons
                const totalPages = Math.ceil(response.total / response.limit);
                pagination.innerHTML = generatePaginationButtons(response.page, totalPages);
            } else {
                hidePagination();
            }
        }

        function hidePagination() {
            const paginationNav = document.getElementById('pagination-nav');
            paginationNav.classList.add('d-none');
        }

        function generatePaginationButtons(currentPage, totalPages) {
            if (totalPages <= 1) return '';

            let buttons = '';

            // Previous button
            buttons += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPage - 1}); return false;">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `;

            // Page number buttons
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust start if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page and ellipsis
            if (startPage > 1) {
                buttons += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="goToPage(1); return false;">1</a>
                    </li>
                `;
                if (startPage > 2) {
                    buttons += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            // Visible page numbers
            for (let i = startPage; i <= endPage; i++) {
                buttons += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToPage(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            // Last page and ellipsis
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    buttons += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                buttons += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="goToPage(${totalPages}); return false;">${totalPages}</a>
                    </li>
                `;
            }

            // Next button
            buttons += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPage + 1}); return false;">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `;

            return buttons;
        }

        function goToPage(page) {
            if (page < 1) return;
            currentPage = page;
            window.currentPage = currentPage;
            loadSaves();
        }

        // Global function to toggle read status
        window.toggleReadStatus = async function(id) {
            try {
                const api = new ApiClient();

                // Get current item to check status
                const currentItems = document.querySelectorAll('.saved-item');
                let currentStatus = 'UNREAD';

                // Find the current status from the DOM
                currentItems.forEach(item => {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge && item.querySelector(`[onclick*="${id}"]`)) {
                        currentStatus = statusBadge.textContent.trim();
                    }
                });

                // Toggle status
                const newStatus = currentStatus === 'UNREAD' ? 'READ' : 'UNREAD';

                await api.put(`/api/save/${id}`, { status: newStatus });
                showToast(`Marked as ${newStatus.toLowerCase()}`, 'success');

                // Refresh the current view
                loadSaves();
            } catch (error) {
                showToast('Failed to update read status', 'danger');
                console.error('Error toggling read status:', error);
            }
        };

        // Global function to create archive
        window.createArchive = function(url) {
            // Show modal with archive options
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Create Archive</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Choose an archive service to save this page:</p>
                            <div class="d-grid gap-2">
                                <a href="https://archive.ph/?run=1&url=${encodeURIComponent(url)}"
                                   target="_blank" class="btn btn-primary">
                                    📁 Archive with Archive.today
                                </a>
                                <a href="https://web.archive.org/save/${encodeURIComponent(url)}"
                                   target="_blank" class="btn btn-info">
                                    🌐 Archive with Wayback Machine
                                </a>
                            </div>
                            <hr>
                            <small class="text-muted">
                                <strong>Archive.today:</strong> Fast archiving, good for recent content<br>
                                <strong>Wayback Machine:</strong> Long-term preservation, historical snapshots
                            </small>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Remove modal from DOM when hidden
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        };

        // Override the search manager's performSearch to use our loadSaves
        window.addEventListener('load', function() {
            if (window.searchManager) {
                window.searchManager.performSearch = loadSaves;
            }
        });
    </script>
</body>
</html>
