Create a GoLang web application using the Gin framework that functions similarly to GetPocket. The application should allow users to save URLs, view their saved URLs, and manage them through a web interface. The app should be lightweight, secure, and accessible from both desktop and mobile browsers. Below are the detailed requirements and guidelines.

Core Features:
1. Framework Choice:
   - Use the Gin framework due to its lightweight nature, high performance, and strong community support.
2. User Authentication:
   - Implement user registration and login using username and password.
   - Use JWT (JSON Web Tokens) for authentication to secure API endpoints.
   - Ensure all API endpoints requiring authentication are protected.
3. API Endpoints:
   - POST /api/save: Save a URL (requires authentication). The request should include the URL and optionally tags or other metadata.
   - GET /api/saves: Retrieve all saved URLs for the authenticated user.
   - DELETE /api/save/{id}: Delete a saved URL (requires authentication).
   - Optionally, PUT /api/save/{id} to update metadata or status (e.g., mark as favorite or archived).
4. Bookmarklet for Easy Saving:
   - Provide a way for users to generate a bookmarklet that includes their JWT token.
   - The bookmarklet should send the current page URL to the /api/save endpoint when clicked.
   - Include a clear warning about the security implications of including the token in the bookmarklet (e.g., risk of token exposure).
5. Web Interface:
   - A page to list all saved URLs for the user (/saves), accessible only to authenticated users.
   - Include options to view details, delete, or edit saved URLs.
   - Implement a search function to filter saved URLs by title, tags, or other metadata.
   - Add options to toggle between light and dark themes.
   - Ensure the interface is responsive and works well on both desktop and mobile browsers.
6. Database:
   - Use SQLite for simplicity (or PostgreSQL for more robustness if needed).
   - Define tables:
     - Users: id, username, password_hash, created_at, updated_at
     - SavedItems: id, user_id, url, title, excerpt, image_url, status (e.g., "UNREAD", "ARCHIVED"), is_favorite, created_at, updated_at
     - Optionally, add tables for tags (e.g., Tags and SavedItemTags) if tagging is implemented.
   - Ensure proper indexing for efficient querying.
7. Metadata Extraction:
   - When saving a URL, fetch the page and extract metadata (title, meta description, first image URL) using goquery.
   - Store metadata in the SavedItems table.
8. Themes:
   - Implement light and dark themes using CSS.
   - Allow users to switch themes and store preference in local storage or user profile.
   - Use CSS variables or separate CSS files for themes.
9. Responsive Design:
   - Use Bootstrap or Tailwind CSS for a responsive web interface.
10. Security:
    - Hash passwords using bcrypt.
    - Validate and sanitize all user inputs.
    - Use HTTPS in production.
11. Deployment:
    - The app should run on a local server, accessible via a browser on a chosen port (e.g., localhost:9191).
    - Certificates are in the /cert folder.
    - Provide instructions for setup, database initialization, and running the server.

Additional Features (Optional):
- Allow users to add notes or comments to saved items.
- Implement a tagging system for organization.
- Add archiving or read/unread status for items.
- Provide RSS feed integration or import/export functionality.
- Add user settings (e.g., default theme).

Data Structure Example:
```json
{
  "edges": [
    {
      "cursor": "some_cursor",
      "node": {
        "_createdAt": 1234567890,
        "_updatedAt": 1234567890,
        "status": "UNREAD",
        "isFavorite": false,
        "tags": [
          {
            "id": "tag_id",
            "name": "tag_name"
          }
        ],
        "item": {
          "isArticle": true,
          "hasImage": "NO_IMAGES",
          "hasVideo": "NO_VIDEOS",
          "timeToRead": 2,
          "shareId": "share_id",
          "itemId": "item_id",
          "givenUrl": "https://example.com",
          "preview": {
            "id": "preview_id",
            "image": {
              "url": "https://example.com/image.jpg"
            },
            "excerpt": "This is an excerpt",
            "title": "Page Title",
            "domain": {
              "name": "example.com"
            },
            "datePublished": "2023-01-01T00:00:00Z",
            "url": "https://example.com"
          }
        },
        "CreatedAtFormatted": "Jan 1, 2023 at 12:00 AM"
      }
    }
  ]
}
```
For simplicity, start with a flatter structure in the database and expand as needed.

Implementation Guidelines:
- Project Structure:
  - main.go: Entry point, sets up Gin router, database connection, and routes.
  - handlers/: Handler functions for API and web routes (e.g., auth.go, save.go).
  - models/: Database models and functions (e.g., user.go, saved_item.go).
  - middleware/: Authentication middleware (e.g., auth_middleware.go).
  - templates/: HTML templates (e.g., saves.html, login.html).
  - static/: CSS, JS, and other assets (e.g., style.css, script.js).
- Database Handling:
  - Use SQLite with GORM or plain SQL queries.
  - Handle migrations if necessary.
- Error Handling:
  - Return meaningful error messages and HTTP status codes.
- Testing:
  - Write unit tests for authentication, API endpoints, and database interactions.
- Performance:
  - Optimize database queries and metadata extraction.
- Security:
  - Use environment variables for sensitive data (e.g., JWT secret).
  - Validate inputs to prevent SQL injection.
- Compatibility:
  - Ensure compatibility to import data from external provider to users saves, example file pocket.json

Instructions for Running:
- Install Go (1.20 or later).
- Install dependencies with `go mod tidy`.
- Set up SQLite database.
- Run with `go run main.go`.
- Access at localhost:8080 (or chosen port).
- Generate bookmarklet after login, e.g.:
  ```javascript
  javascript:(function(){window.open('http://localhost:9191/api/save?url='+encodeURIComponent(window.location.href)+'&token=your_jwt_token','_blank')})();
  ```

Security Warning for Bookmarklet:
- Warn users that the bookmarklet includes their JWT token, which could be exposed if shared. Recommend using it only on trusted devices.

Future Enhancements:
- Consider OAuth for secure authentication.
- Add multi-device syncing.
- Enhance metadata extraction with advanced libraries.