package main

import (
	"log"
	"os"

	"save-it/handlers"
	"save-it/middleware"
	"save-it/models"
	"save-it/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Database instance
var db *gorm.DB

func main() {
	// Initialize database
	var err error
	db, err = models.InitDatabase("save-it.db")
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Initialize Gin router
	r := gin.Default()

	// Set max multipart memory to 50MB for file uploads
	r.MaxMultipartMemory = 50 << 20 // 50 MB

	// Load HTML templates
	r.LoadHTMLGlob("templates/*")

	// Serve static files
	r.Static("/static", "./static")

	// Basic routes
	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "Save-It - Your Personal URL Saver",
		})
	})

	// Authentication pages
	r.GET("/login", func(c *gin.Context) {
		c.HTML(200, "login.html", nil)
	})

	r.GET("/register", func(c *gin.Context) {
		c.HTML(200, "register.html", nil)
	})

	r.GET("/saves", func(c *gin.Context) {
		c.HTML(200, "saves.html", nil)
	})

	r.GET("/import", func(c *gin.Context) {
		c.HTML(200, "import.html", gin.H{
			"title": "Import Data - Save-It",
		})
	})

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Save-It server is running",
		})
	})

	// Initialize backup services
	backupService := services.NewBackupService(db, "backups", 30)
	if err := backupService.Initialize(); err != nil {
		log.Printf("Warning: Failed to initialize backup service: %v", err)
	}

	backupScheduler := services.NewBackupScheduler(backupService)

	// Start automatic backup scheduler with default config
	config := services.GetDefaultScheduleConfig()
	backupScheduler.Start(config)
	log.Println("Automatic backup scheduler started")

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db)
	savesHandler := handlers.NewSavesHandler(db)
	importHandler := handlers.NewImportHandler(db)
	backupHandler := handlers.NewBackupHandler(backupService, backupScheduler)

	// API routes
	api := r.Group("/api")
	{
		// Authentication routes (no auth required)
		api.POST("/register", authHandler.Register)
		api.POST("/login", authHandler.Login)
		api.POST("/validate-token", authHandler.ValidateToken)

		// Protected routes (require authentication)
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware(db))
		{
			// User routes
			protected.GET("/user", authHandler.GetCurrentUser)
			protected.POST("/refresh-token", authHandler.RefreshToken)
			protected.POST("/logout", authHandler.Logout)
			protected.POST("/change-password", authHandler.ChangePassword)

			// Token management routes
			protected.GET("/token", authHandler.GetCurrentToken)
			protected.POST("/token/generate", authHandler.GenerateNewToken)

			// Saved items routes (most use standard auth)
			protected.GET("/saves", savesHandler.GetSavedItems)
			protected.GET("/save/:id", savesHandler.GetSavedItem)
			protected.PUT("/save/:id", savesHandler.UpdateSavedItem)
			protected.DELETE("/save/:id", savesHandler.DeleteSavedItem)
			protected.DELETE("/saves/clear-all", savesHandler.ClearAllSaves)

			// Import routes
			protected.POST("/import/pocket", importHandler.ImportPocketData)

			// Backup routes (admin/user level)
			protected.POST("/backup/create", backupHandler.CreateManualBackup)
			protected.GET("/backup/list", backupHandler.ListBackups)
			protected.GET("/backup/stats", backupHandler.GetBackupStats)
			protected.GET("/backup/download/:filename", backupHandler.DownloadBackup)
			protected.POST("/backup/restore/:filename", backupHandler.RestoreBackup)
			protected.DELETE("/backup/delete/:filename", backupHandler.DeleteBackup)
			protected.GET("/backup/scheduler/status", backupHandler.GetSchedulerStatus)
			protected.POST("/backup/scheduler/start", backupHandler.StartScheduler)
			protected.POST("/backup/scheduler/stop", backupHandler.StopScheduler)
			protected.POST("/backup/immediate", backupHandler.CreateImmediateBackup)
		}

		// Special routes that support both Bearer token and query parameter authentication
		// This is useful for bookmarklets and external integrations
		flexible := api.Group("/")
		flexible.Use(middleware.FlexibleAuthMiddleware(db))
		{
			// Save URL endpoint - supports both auth methods for bookmarklets
			flexible.POST("/save", savesHandler.SaveURL)
		}

		// Test route (no auth required for testing)
		api.POST("/import/test-local", importHandler.TestImportLocalFile)
	}

	// Get port from environment or default to 9191
	port := os.Getenv("PORT")
	if port == "" {
		port = "9191"
	}

	// Check if SSL certificates exist
	certFile := "cert/lumakuhome.com-2023-07-16-064135.cer"
	keyFile := "cert/lumakuhome.com-2023-07-16-064135.pkey"

	// Check if certificate files exist
	if _, err := os.Stat(certFile); err == nil {
		if _, err := os.Stat(keyFile); err == nil {
			// Start HTTPS server
			log.Printf("Starting Save-It HTTPS server on port %s", port)
			log.Printf("Access the application at: https://ploppy.ddnsgeek.com:%s", port)
			log.Printf("Certificate: %s", certFile)
			log.Printf("Private Key: %s", keyFile)

			if err := r.RunTLS(":"+port, certFile, keyFile); err != nil {
				log.Fatal("Failed to start HTTPS server:", err)
			}
		} else {
			log.Printf("Private key file not found: %s", keyFile)
			log.Printf("Falling back to HTTP server")
			startHTTPServer(r, port)
		}
	} else {
		log.Printf("Certificate file not found: %s", certFile)
		log.Printf("Falling back to HTTP server")
		startHTTPServer(r, port)
	}
}

// startHTTPServer starts the server in HTTP mode
func startHTTPServer(r *gin.Engine, port string) {
	log.Printf("Starting Save-It HTTP server on port %s", port)
	log.Printf("Access the application at: http://localhost:%s", port)

	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start HTTP server:", err)
	}
}
