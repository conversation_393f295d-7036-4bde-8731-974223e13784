package handlers

import (
	"net/http"
	"save-it/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BackupHandler handles backup-related HTTP requests
type BackupHandler struct {
	backupService   *services.BackupService
	backupScheduler *services.BackupScheduler
}

// NewBackupHandler creates a new backup handler
func NewBackupHandler(backupService *services.BackupService, backupScheduler *services.BackupScheduler) *BackupHandler {
	return &BackupHandler{
		backupService:   backupService,
		backupScheduler: backupScheduler,
	}
}

// CreateManualBackup creates a manual backup
func (h *BackupHandler) CreateManualBackup(c *gin.Context) {
	backup, err := h.backupService.CreateBackup("manual")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create backup",
			"details": err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"message": "Backup created successfully",
		"backup":  backup,
	})
}

// ListBackups returns a list of all available backups
func (h *BackupHandler) ListBackups(c *gin.Context) {
	backups, err := h.backupService.ListBackups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list backups",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"backups": backups,
		"count":   len(backups),
	})
}

// GetBackupStats returns backup statistics
func (h *BackupHandler) GetBackupStats(c *gin.Context) {
	stats, err := h.backupService.GetBackupStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get backup statistics",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
	})
}

// DownloadBackup allows downloading a specific backup file
func (h *BackupHandler) DownloadBackup(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filename parameter is required",
		})
		return
	}

	// List backups to verify the file exists and get its path
	backups, err := h.backupService.ListBackups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list backups",
			"details": err.Error(),
		})
		return
	}

	var targetBackup *services.BackupInfo
	for _, backup := range backups {
		if backup.Filename == filename {
			targetBackup = backup
			break
		}
	}

	if targetBackup == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Backup file not found",
		})
		return
	}

	if targetBackup.IsCorrupted {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Backup file is corrupted and cannot be downloaded",
		})
		return
	}

	// Set headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", strconv.FormatInt(targetBackup.Size, 10))

	// Serve the file
	c.File(targetBackup.Path)
}

// RestoreBackup restores the database from a backup
func (h *BackupHandler) RestoreBackup(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filename parameter is required",
		})
		return
	}

	// List backups to verify the file exists and get its path
	backups, err := h.backupService.ListBackups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list backups",
			"details": err.Error(),
		})
		return
	}

	var targetBackup *services.BackupInfo
	for _, backup := range backups {
		if backup.Filename == filename {
			targetBackup = backup
			break
		}
	}

	if targetBackup == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Backup file not found",
		})
		return
	}

	if targetBackup.IsCorrupted {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Cannot restore from corrupted backup",
		})
		return
	}

	// Perform the restoration
	if err := h.backupService.RestoreBackup(targetBackup.Path); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to restore backup",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Database restored successfully from backup",
		"backup":  targetBackup,
	})
}

// DeleteBackup deletes a specific backup file
func (h *BackupHandler) DeleteBackup(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Filename parameter is required",
		})
		return
	}

	// List backups to verify the file exists and get its path
	backups, err := h.backupService.ListBackups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list backups",
			"details": err.Error(),
		})
		return
	}

	var targetBackup *services.BackupInfo
	for _, backup := range backups {
		if backup.Filename == filename {
			targetBackup = backup
			break
		}
	}

	if targetBackup == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Backup file not found",
		})
		return
	}

	// Delete the file
	if err := h.backupService.DeleteBackup(targetBackup.Path); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete backup",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Backup deleted successfully",
		"backup":  targetBackup,
	})
}

// GetSchedulerStatus returns the current status of the backup scheduler
func (h *BackupHandler) GetSchedulerStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"scheduler_running": h.backupScheduler.IsRunning(),
	})
}

// StartScheduler starts the backup scheduler
func (h *BackupHandler) StartScheduler(c *gin.Context) {
	if h.backupScheduler.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Backup scheduler is already running",
		})
		return
	}

	// Use default configuration for now
	config := services.GetDefaultScheduleConfig()
	h.backupScheduler.Start(config)

	c.JSON(http.StatusOK, gin.H{
		"message": "Backup scheduler started successfully",
		"config":  config,
	})
}

// StopScheduler stops the backup scheduler
func (h *BackupHandler) StopScheduler(c *gin.Context) {
	if !h.backupScheduler.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Backup scheduler is not running",
		})
		return
	}

	h.backupScheduler.Stop()

	c.JSON(http.StatusOK, gin.H{
		"message": "Backup scheduler stopped successfully",
	})
}

// CreateImmediateBackup creates an immediate backup for testing
func (h *BackupHandler) CreateImmediateBackup(c *gin.Context) {
	backupType := c.DefaultQuery("type", "manual")

	backup, err := h.backupScheduler.CreateImmediateBackup(backupType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create immediate backup",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Immediate backup created successfully",
		"backup":  backup,
	})
}
