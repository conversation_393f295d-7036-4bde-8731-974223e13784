<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>Save-It</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/saves">My Saves</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/import">Import</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <span id="username-display">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="manage-token-btn">🔑 Manage API Token</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="change-password-btn">Change Password</a></li>
                            <li><a class="dropdown-item text-danger" href="#" id="clear-all-saves-btn">Clear All Saves</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">Import Your Data</h2>
                        <p class="card-text">
                            Import your saved URLs from other services. Currently supported formats:
                        </p>

                        <div class="alert alert-info">
                            <h6><strong>Pocket Export</strong></h6>
                            <p class="mb-0">
                                Export your data from Pocket and upload the JSON file here.
                                Go to <a href="https://getpocket.com/export" target="_blank">getpocket.com/export</a>
                                to download your data.
                            </p>
                        </div>

                        <div id="import-status" class="alert d-none"></div>

                        <form id="import-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="import-file" class="form-label">Select File</label>
                                <input type="file" class="form-control" id="import-file" name="file"
                                       accept=".json" required>
                                <div class="form-text">
                                    Supported formats: JSON files from Pocket export (max 50MB)
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skip-duplicates" checked>
                                    <label class="form-check-label" for="skip-duplicates">
                                        Skip duplicate URLs (recommended)
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary" id="import-btn">
                                    <span class="spinner-border spinner-border-sm d-none" id="import-spinner"></span>
                                    <span id="import-btn-text">Import Data</span>
                                </button>
                                <a href="/saves" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </form>

                        <div id="import-progress" class="mt-4 d-none">
                            <h6>Import Progress</h6>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted" id="progress-text">Preparing import...</small>
                            </div>
                        </div>

                        <div id="import-results" class="mt-4 d-none">
                            <h6>Import Results</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-success" id="imported-count">0</div>
                                        <small class="text-muted">Imported</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-warning" id="skipped-count">0</div>
                                        <small class="text-muted">Skipped</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-info" id="total-count">0</div>
                                        <small class="text-muted">Total</small>
                                    </div>
                                </div>
                            </div>

                            <div id="import-errors" class="mt-3 d-none">
                                <h6 class="text-danger">Errors</h6>
                                <div class="alert alert-danger">
                                    <ul id="error-list" class="mb-0"></ul>
                                </div>
                            </div>

                            <div class="mt-3">
                                <a href="/saves" class="btn btn-success">View My Saves</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">How to Export from Pocket</h5>
                        <ol>
                            <li>Go to <a href="https://getpocket.com/export" target="_blank">getpocket.com/export</a></li>
                            <li>Click "Export" to download your data</li>
                            <li>Save the downloaded JSON file to your computer</li>
                            <li>Upload the file using the form above</li>
                        </ol>

                        <div class="alert alert-warning">
                            <strong>Note:</strong> The import process will automatically extract metadata
                            for items that don't have titles. This may take some time for large imports.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="change-password-error" class="alert alert-danger d-none"></div>
                    <form id="change-password-form">
                        <div class="mb-3">
                            <label for="current-password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current-password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new-password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new-password" name="new_password" required minlength="6">
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm-password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm-password" name="confirm_password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="change-password-submit-btn">
                        <span class="spinner-border spinner-border-sm d-none" id="change-password-spinner"></span>
                        Change Password
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear All Saves Modal -->
    <div class="modal fade" id="clearAllSavesModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">⚠️ Clear All Saves</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="clear-all-saves-error" class="alert alert-danger d-none"></div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> This action cannot be undone!
                    </div>
                    <p>Are you sure you want to delete <strong>ALL</strong> of your saved items? This will permanently remove:</p>
                    <ul>
                        <li>All saved URLs</li>
                        <li>All metadata (titles, excerpts, etc.)</li>
                        <li>All read/unread status</li>
                        <li>All favorite markings</li>
                    </ul>
                    <p class="text-muted">This action is irreversible. Consider exporting your data first if you want to keep a backup.</p>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="confirm-clear-all">
                        <label class="form-check-label" for="confirm-clear-all">
                            I understand this action cannot be undone
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="clear-all-saves-submit-btn" disabled>
                        <span class="spinner-border spinner-border-sm d-none" id="clear-all-saves-spinner"></span>
                        Delete All Saves
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Token Management Modal -->
    <div class="modal fade" id="tokenManagementModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🔑 API Token Management</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="token-management-error" class="alert alert-danger d-none"></div>

                    <div class="alert alert-info">
                        <strong>ℹ️ About API Tokens:</strong><br>
                        API tokens allow you to save URLs from external tools like bookmarklets. Only one token is active at a time.
                        Keep your token secure and don't share it with others.
                    </div>

                    <!-- Current Token Section -->
                    <div id="current-token-section" class="mb-4">
                        <h6>Current Token</h6>
                        <div id="token-display" class="d-none">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <small class="text-muted">Token:</small>
                                            <div class="font-monospace small text-break" id="current-token-value"></div>
                                            <small class="text-muted">Expires: <span id="token-expires"></span></small>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="copy-token-btn">
                                                📋 Copy
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="no-token-display" class="text-muted">
                            <em>No current token available. Generate a new one below.</em>
                        </div>
                    </div>

                    <!-- Bookmarklet Section -->
                    <div id="bookmarklet-section" class="mb-4 d-none">
                        <h6>Bookmarklet</h6>
                        <p class="small text-muted">Drag this bookmarklet to your bookmarks bar to quickly save URLs:</p>
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <a href="#" id="bookmarklet-link" class="btn btn-outline-primary btn-sm">
                                            📌 Save to Save-It
                                        </a>
                                        <div class="small text-muted mt-1">Drag this button to your bookmarks bar</div>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="copy-bookmarklet-btn">
                                            📋 Copy Code
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Section -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" id="generate-token-btn">
                            <span class="spinner-border spinner-border-sm d-none" id="generate-token-spinner"></span>
                            🔄 Generate New Token
                        </button>
                        <small class="text-muted text-center">
                            Generating a new token will invalidate the previous one.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Set username in navbar
            if (user.username) {
                document.getElementById('username-display').textContent = user.username;
            }

            // Set up event listeners
            setupEventListeners();
        });

        function setupEventListeners() {
            // Logout
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user');
                window.location.href = '/login';
            });

            // Change Password
            document.getElementById('change-password-btn').addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
                modal.show();
            });

            // Clear All Saves
            document.getElementById('clear-all-saves-btn').addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('clearAllSavesModal'));
                modal.show();
            });

            // Token Management
            document.getElementById('manage-token-btn').addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('tokenManagementModal'));
                modal.show();
                loadCurrentToken();
            });

            // Change Password Form
            document.getElementById('change-password-submit-btn').addEventListener('click', changePassword);

            // Clear All Saves Form
            document.getElementById('clear-all-saves-submit-btn').addEventListener('click', clearAllSaves);

            // Enable/disable clear all saves button based on checkbox
            document.getElementById('confirm-clear-all').addEventListener('change', function() {
                document.getElementById('clear-all-saves-submit-btn').disabled = !this.checked;
            });

            // Token Management Form
            document.getElementById('generate-token-btn').addEventListener('click', generateNewToken);
            document.getElementById('copy-token-btn').addEventListener('click', copyTokenToClipboard);
            document.getElementById('copy-bookmarklet-btn').addEventListener('click', copyBookmarkletToClipboard);

            // Import form
            document.getElementById('import-form').addEventListener('submit', handleImport);
        }

        async function handleImport(e) {
            e.preventDefault();

            const form = e.target;
            const fileInput = document.getElementById('import-file');
            const importBtn = document.getElementById('import-btn');
            const spinner = document.getElementById('import-spinner');
            const btnText = document.getElementById('import-btn-text');
            const statusDiv = document.getElementById('import-status');
            const progressDiv = document.getElementById('import-progress');
            const resultsDiv = document.getElementById('import-results');

            // Validate file
            if (!fileInput.files[0]) {
                showStatus('Please select a file to import.', 'danger');
                return;
            }

            // Show loading state
            importBtn.disabled = true;
            spinner.classList.remove('d-none');
            btnText.textContent = 'Importing...';
            statusDiv.classList.add('d-none');
            progressDiv.classList.remove('d-none');
            resultsDiv.classList.add('d-none');

            try {
                const formData = new FormData(form);
                const token = localStorage.getItem('auth_token');

                const response = await fetch('/api/import/pocket', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showImportResults(result);
                    showStatus(result.message, 'success');
                } else {
                    throw new Error(result.error || 'Import failed');
                }

            } catch (error) {
                showStatus(`Import failed: ${error.message}`, 'danger');
            } finally {
                // Reset form state
                importBtn.disabled = false;
                spinner.classList.add('d-none');
                btnText.textContent = 'Import Data';
                progressDiv.classList.add('d-none');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('import-status');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('d-none');
        }

        function showImportResults(result) {
            const resultsDiv = document.getElementById('import-results');

            document.getElementById('imported-count').textContent = result.imported_items;
            document.getElementById('skipped-count').textContent = result.skipped_items;
            document.getElementById('total-count').textContent = result.total_items;

            // Show errors if any
            if (result.errors && result.errors.length > 0) {
                const errorsDiv = document.getElementById('import-errors');
                const errorList = document.getElementById('error-list');

                errorList.innerHTML = result.errors.map(error => `<li>${error}</li>`).join('');
                errorsDiv.classList.remove('d-none');
            }

            resultsDiv.classList.remove('d-none');
        }

        async function changePassword() {
            const currentPasswordInput = document.getElementById('current-password');
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const submitBtn = document.getElementById('change-password-submit-btn');
            const spinner = document.getElementById('change-password-spinner');
            const errorDiv = document.getElementById('change-password-error');

            const currentPassword = currentPasswordInput.value.trim();
            const newPassword = newPasswordInput.value.trim();
            const confirmPassword = confirmPasswordInput.value.trim();

            // Validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                errorDiv.textContent = 'All fields are required';
                errorDiv.classList.remove('d-none');
                return;
            }

            if (newPassword.length < 6) {
                errorDiv.textContent = 'New password must be at least 6 characters long';
                errorDiv.classList.remove('d-none');
                return;
            }

            if (newPassword !== confirmPassword) {
                errorDiv.textContent = 'New passwords do not match';
                errorDiv.classList.remove('d-none');
                return;
            }

            try {
                submitBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                await api.post('/api/change-password', {
                    current_password: currentPassword,
                    new_password: newPassword
                });

                // Close modal and show success
                const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                modal.hide();

                // Clear form
                currentPasswordInput.value = '';
                newPasswordInput.value = '';
                confirmPasswordInput.value = '';

                showToast('Password changed successfully!', 'success');

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                submitBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        async function clearAllSaves() {
            const submitBtn = document.getElementById('clear-all-saves-submit-btn');
            const spinner = document.getElementById('clear-all-saves-spinner');
            const errorDiv = document.getElementById('clear-all-saves-error');

            try {
                submitBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                const response = await api.delete('/api/saves/clear-all');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('clearAllSavesModal'));
                modal.hide();

                // Reset checkbox
                document.getElementById('confirm-clear-all').checked = false;

                showToast(`All saves cleared successfully! (${response.deleted_count} items deleted)`, 'success');

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                submitBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        // Token Management Functions (same as in saves.html)
        async function loadCurrentToken() {
            const errorDiv = document.getElementById('token-management-error');

            try {
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                const response = await api.get('/api/token');

                displayToken(response);

            } catch (error) {
                // If no token exists, show the no-token state
                displayNoToken();
            }
        }

        function displayToken(tokenData) {
            const tokenDisplay = document.getElementById('token-display');
            const noTokenDisplay = document.getElementById('no-token-display');
            const bookmarkletSection = document.getElementById('bookmarklet-section');

            // Show token
            document.getElementById('current-token-value').textContent = tokenData.token;
            document.getElementById('token-expires').textContent = new Date(tokenData.expires_at).toLocaleString();

            tokenDisplay.classList.remove('d-none');
            noTokenDisplay.classList.add('d-none');
            bookmarkletSection.classList.remove('d-none');

            // Update bookmarklet
            updateBookmarklet(tokenData.token);
        }

        function displayNoToken() {
            const tokenDisplay = document.getElementById('token-display');
            const noTokenDisplay = document.getElementById('no-token-display');
            const bookmarkletSection = document.getElementById('bookmarklet-section');

            tokenDisplay.classList.add('d-none');
            noTokenDisplay.classList.remove('d-none');
            bookmarkletSection.classList.add('d-none');
        }

        function updateBookmarklet(token) {
            const bookmarkletLink = document.getElementById('bookmarklet-link');
            const baseUrl = window.location.protocol + '//' + window.location.host;
            const bookmarkletCode = `javascript:(function(){window.open('${baseUrl}/api/save?url='+encodeURIComponent(window.location.href)+'&token=${token}','_blank')})();`;

            bookmarkletLink.href = bookmarkletCode;
        }

        async function generateNewToken() {
            const submitBtn = document.getElementById('generate-token-btn');
            const spinner = document.getElementById('generate-token-spinner');
            const errorDiv = document.getElementById('token-management-error');

            try {
                submitBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                const response = await api.post('/api/token/generate');

                displayToken(response);
                showToast('New API token generated successfully!', 'success');

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                submitBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        async function copyTokenToClipboard() {
            const tokenValue = document.getElementById('current-token-value').textContent;

            try {
                await navigator.clipboard.writeText(tokenValue);
                showToast('Token copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = tokenValue;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Token copied to clipboard!', 'success');
            }
        }

        async function copyBookmarkletToClipboard() {
            const bookmarkletCode = document.getElementById('bookmarklet-link').href;

            try {
                await navigator.clipboard.writeText(bookmarkletCode);
                showToast('Bookmarklet code copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = bookmarkletCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Bookmarklet code copied to clipboard!', 'success');
            }
        }
    </script>
</body>
</html>
