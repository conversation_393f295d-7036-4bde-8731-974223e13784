<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>Save-It</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/saves">My Saves</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/import">Import</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <span id="username-display">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/bookmarklet">Bookmarklet</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">Import Your Data</h2>
                        <p class="card-text">
                            Import your saved URLs from other services. Currently supported formats:
                        </p>

                        <div class="alert alert-info">
                            <h6><strong>Pocket Export</strong></h6>
                            <p class="mb-0">
                                Export your data from Pocket and upload the JSON file here.
                                Go to <a href="https://getpocket.com/export" target="_blank">getpocket.com/export</a>
                                to download your data.
                            </p>
                        </div>

                        <div id="import-status" class="alert d-none"></div>

                        <form id="import-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="import-file" class="form-label">Select File</label>
                                <input type="file" class="form-control" id="import-file" name="file"
                                       accept=".json" required>
                                <div class="form-text">
                                    Supported formats: JSON files from Pocket export (max 50MB)
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skip-duplicates" checked>
                                    <label class="form-check-label" for="skip-duplicates">
                                        Skip duplicate URLs (recommended)
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary" id="import-btn">
                                    <span class="spinner-border spinner-border-sm d-none" id="import-spinner"></span>
                                    <span id="import-btn-text">Import Data</span>
                                </button>
                                <a href="/saves" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </form>

                        <div id="import-progress" class="mt-4 d-none">
                            <h6>Import Progress</h6>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted" id="progress-text">Preparing import...</small>
                            </div>
                        </div>

                        <div id="import-results" class="mt-4 d-none">
                            <h6>Import Results</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-success" id="imported-count">0</div>
                                        <small class="text-muted">Imported</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-warning" id="skipped-count">0</div>
                                        <small class="text-muted">Skipped</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h4 text-info" id="total-count">0</div>
                                        <small class="text-muted">Total</small>
                                    </div>
                                </div>
                            </div>

                            <div id="import-errors" class="mt-3 d-none">
                                <h6 class="text-danger">Errors</h6>
                                <div class="alert alert-danger">
                                    <ul id="error-list" class="mb-0"></ul>
                                </div>
                            </div>

                            <div class="mt-3">
                                <a href="/saves" class="btn btn-success">View My Saves</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">How to Export from Pocket</h5>
                        <ol>
                            <li>Go to <a href="https://getpocket.com/export" target="_blank">getpocket.com/export</a></li>
                            <li>Click "Export" to download your data</li>
                            <li>Save the downloaded JSON file to your computer</li>
                            <li>Upload the file using the form above</li>
                        </ol>

                        <div class="alert alert-warning">
                            <strong>Note:</strong> The import process will automatically extract metadata
                            for items that don't have titles. This may take some time for large imports.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Set username in navbar
            if (user.username) {
                document.getElementById('username-display').textContent = user.username;
            }

            // Set up event listeners
            setupEventListeners();
        });

        function setupEventListeners() {
            // Logout
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user');
                window.location.href = '/login';
            });

            // Import form
            document.getElementById('import-form').addEventListener('submit', handleImport);
        }

        async function handleImport(e) {
            e.preventDefault();

            const form = e.target;
            const fileInput = document.getElementById('import-file');
            const importBtn = document.getElementById('import-btn');
            const spinner = document.getElementById('import-spinner');
            const btnText = document.getElementById('import-btn-text');
            const statusDiv = document.getElementById('import-status');
            const progressDiv = document.getElementById('import-progress');
            const resultsDiv = document.getElementById('import-results');

            // Validate file
            if (!fileInput.files[0]) {
                showStatus('Please select a file to import.', 'danger');
                return;
            }

            // Show loading state
            importBtn.disabled = true;
            spinner.classList.remove('d-none');
            btnText.textContent = 'Importing...';
            statusDiv.classList.add('d-none');
            progressDiv.classList.remove('d-none');
            resultsDiv.classList.add('d-none');

            try {
                const formData = new FormData(form);
                const token = localStorage.getItem('auth_token');

                const response = await fetch('/api/import/pocket', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showImportResults(result);
                    showStatus(result.message, 'success');
                } else {
                    throw new Error(result.error || 'Import failed');
                }

            } catch (error) {
                showStatus(`Import failed: ${error.message}`, 'danger');
            } finally {
                // Reset form state
                importBtn.disabled = false;
                spinner.classList.add('d-none');
                btnText.textContent = 'Import Data';
                progressDiv.classList.add('d-none');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('import-status');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('d-none');
        }

        function showImportResults(result) {
            const resultsDiv = document.getElementById('import-results');

            document.getElementById('imported-count').textContent = result.imported_items;
            document.getElementById('skipped-count').textContent = result.skipped_items;
            document.getElementById('total-count').textContent = result.total_items;

            // Show errors if any
            if (result.errors && result.errors.length > 0) {
                const errorsDiv = document.getElementById('import-errors');
                const errorList = document.getElementById('error-list');

                errorList.innerHTML = result.errors.map(error => `<li>${error}</li>`).join('');
                errorsDiv.classList.remove('d-none');
            }

            resultsDiv.classList.remove('d-none');
        }
    </script>
</body>
</html>
